import { useState } from 'react';
import { Search, Database, Gamepad2 } from 'lucide-react';
import { Game } from '../types';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';

// Helper function to normalize game titles for deduplication
function normalizeTitle(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s]/g, '') // Remove special characters
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

// Helper function to check if two games are duplicates
function areGamesDuplicate(game1: Game, game2: Game): boolean {
  const title1 = normalizeTitle(game1.title);
  const title2 = normalizeTitle(game2.title);

  // Check if titles are exactly the same
  if (title1 === title2 && game1.platform === game2.platform) return true;

  // Check for common variations
  const variations = [
    [' edition', ''],
    [' remastered', ''],
    [' definitive', ''],
    [' goty', ''],
    [' game of the year', ''],
    [' directors cut', ''],
    [' enhanced', ''],
    [' ultimate', ''],
    [' deluxe', ''],
    [' complete', ''],
    [' hd', ''],
    [' remake', ''],
    [' reboot', '']
  ];

  let cleanTitle1 = title1;
  let cleanTitle2 = title2;

  variations.forEach(([variation, replacement]) => {
    cleanTitle1 = cleanTitle1.replace(variation, replacement);
    cleanTitle2 = cleanTitle2.replace(variation, replacement);
  });

  return cleanTitle1 === cleanTitle2 && game1.platform === game2.platform;
}

// Function to deduplicate games while preserving the best data
function deduplicateGames(games: Game[]): Game[] {
  const uniqueGames: Game[] = [];

  for (const game of games) {
    const existingIndex = uniqueGames.findIndex(existing =>
      areGamesDuplicate(game, existing)
    );

    if (existingIndex === -1) {
      // No duplicate found, add the game
      uniqueGames.push(game);
    } else {
      // Duplicate found, merge the data keeping the best information
      const existing = uniqueGames[existingIndex];
      const merged: Game = {
        ...existing,
        // Prefer non-empty values
        title: game.title.length > existing.title.length ? game.title : existing.title,
        description: game.description || existing.description,
        cover_image: game.cover_image || existing.cover_image,
        screenshots: [...(existing.screenshots || []), ...(game.screenshots || [])].slice(0, 10), // Limit screenshots
        developer: game.developer || existing.developer,
        publisher: game.publisher || existing.publisher,
        release_date: game.release_date || existing.release_date,
        metacritic_score: game.metacritic_score || existing.metacritic_score,
        genres: [...new Set([...(existing.genres || []), ...(game.genres || [])])], // Merge unique genres
      };
      uniqueGames[existingIndex] = merged;
    }
  }

  return uniqueGames;
}

interface GameSearchProps {
  onSearchResults: (games: Game[]) => void;
  onLoading: (loading: boolean) => void;
  onError: (error: string | null) => void;
}

export function GameSearch({ onSearchResults, onLoading, onError }: GameSearchProps) {
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchTerm.trim()) {
      onError('Please enter a search term');
      return;
    }

    onLoading(true);
    onError(null);

    try {
      // Import the API client dynamically to avoid circular dependencies
      const { gameAPI } = await import('../lib/api');

      // Search both APIs with higher limits for comprehensive results
      const results = await gameAPI.searchBothAPIs(searchTerm.trim(), 40);

      // Convert IGDB games to our Game interface - create separate entries for each platform
      const igdbGames: Game[] = [];
      results.igdb.forEach(igdbGame => {
        const platforms = igdbGame.platforms || [{ name: 'Unknown' }];
        platforms.forEach((platform, index) => {
          igdbGames.push({
            id: `igdb_${igdbGame.id}_${index}`,
            title: igdbGame.name,
            platform: platform.name as any,
            genres: (igdbGame.genres?.map(g => g.name) || []) as any[],
            developer: igdbGame.involved_companies?.find(c => c.developer)?.company.name,
            publisher: igdbGame.involved_companies?.find(c => c.publisher)?.company.name,
            release_date: igdbGame.first_release_date
              ? new Date(igdbGame.first_release_date * 1000).toISOString().split('T')[0]
              : undefined,
            description: igdbGame.summary,
            cover_image: igdbGame.cover?.url ? `https:${igdbGame.cover.url.replace('t_thumb', 't_cover_big')}` : undefined,
            screenshots: igdbGame.screenshots?.map(s => `https:${s.url.replace('t_thumb', 't_screenshot_med')}`) || [],
            metacritic_score: igdbGame.aggregated_rating ? Math.round(igdbGame.aggregated_rating) : undefined,
            igdb_id: igdbGame.id.toString(),
          });
        });
      });

      // Convert RAWG games to our Game interface - create separate entries for each platform
      const rawgGames: Game[] = [];
      results.rawg.forEach(rawgGame => {
        const platforms = rawgGame.platforms || [{ platform: { name: 'Unknown' } }];
        platforms.forEach((platformObj, index) => {
          rawgGames.push({
            id: `rawg_${rawgGame.id}_${index}`,
            title: rawgGame.name,
            platform: platformObj.platform?.name as any,
            genres: (rawgGame.genres?.map(g => g.name) || []) as any[],
            developer: rawgGame.developers?.[0]?.name,
            publisher: rawgGame.publishers?.[0]?.name,
            release_date: rawgGame.released,
            description: rawgGame.description_raw,
            cover_image: rawgGame.background_image,
            screenshots: rawgGame.short_screenshots?.map(s => s.image) || [],
            metacritic_score: rawgGame.metacritic,
            igdb_id: `rawg_${rawgGame.id}`,
          });
        });
      });

      // Combine and deduplicate results from both APIs
      const allGames = [...igdbGames, ...rawgGames];
      const deduplicatedGames = deduplicateGames(allGames);

      console.log(`Found ${igdbGames.length} IGDB games, ${rawgGames.length} RAWG games`);
      console.log(`After deduplication: ${deduplicatedGames.length} unique games`);

      onSearchResults(deduplicatedGames);
    } catch (error) {
      console.error('Search error:', error);
      onError(error instanceof Error ? error.message : 'Failed to search games');
      onSearchResults([]);
    } finally {
      onLoading(false);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="p-3 bg-primary/10 rounded-2xl">
            <Search className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-5xl font-bold bg-gradient-to-r from-primary to-orange-600 bg-clip-text text-transparent">
            Game Discovery
          </h1>
        </div>
        <p className="text-muted-foreground text-lg mb-6">
          Discover games across all platforms with comprehensive search
        </p>
        <div className="flex justify-center gap-3">
          <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
            <Database className="h-3 w-3 mr-1" />
            IGDB API
          </Badge>
          <Badge variant="secondary" className="bg-orange-100 text-orange-700 border-orange-200">
            <Gamepad2 className="h-3 w-3 mr-1" />
            RAWG API
          </Badge>
        </div>
      </div>

      <Card className="border-2 border-primary/20 shadow-lg">
        <CardContent className="p-6">
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search for any game title..."
                className="pl-10 h-12 text-lg border-primary/20 focus:border-primary"
              />
            </div>
            <Button
              type="submit"
              size="lg"
              className="h-12 px-8 bg-gradient-to-r from-primary to-orange-600 hover:from-primary/90 hover:to-orange-600/90"
            >
              <Search className="h-5 w-5 mr-2" />
              Search Games
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
