import { useState } from 'react';

interface PlatformFilterProps {
  onFilterChange: (selectedPlatforms: string[]) => void;
  gameCount: number;
  allGames?: Array<{ platform?: string }>;
}

interface PlatformOption {
  id: string;
  name: string;
  icon: string;
  color: string;
  keywords: string[];
}

const platformOptions: PlatformOption[] = [
  {
    id: 'playstation',
    name: 'PlayStation',
    icon: '🎮',
    color: 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100',
    keywords: ['playstation', 'ps1', 'ps2', 'ps3', 'ps4', 'ps5', 'psp', 'vita']
  },
  {
    id: 'xbox',
    name: 'Xbox',
    icon: '🎯',
    color: 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100',
    keywords: ['xbox', 'xbox 360', 'xbox one', 'xbox series']
  },
  {
    id: 'nintendo',
    name: 'Nintendo',
    icon: '🕹️',
    color: 'bg-red-50 text-red-700 border-red-200 hover:bg-red-100',
    keywords: ['nintendo', 'switch', 'wii', 'gamecube', '3ds', 'ds', 'nes', 'snes']
  },
  {
    id: 'pc',
    name: 'PC',
    icon: '💻',
    color: 'bg-slate-50 text-slate-700 border-slate-200 hover:bg-slate-100',
    keywords: ['pc', 'windows', 'microsoft windows', 'steam', 'epic']
  },
  {
    id: 'mac',
    name: 'Mac',
    icon: '🖥️',
    color: 'bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100',
    keywords: ['mac', 'macos', 'apple']
  },
  {
    id: 'mobile',
    name: 'Mobile',
    icon: '📱',
    color: 'bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100',
    keywords: ['ios', 'android', 'iphone', 'ipad', 'mobile']
  },
  {
    id: 'steam',
    name: 'Steam Deck',
    icon: '🚂',
    color: 'bg-indigo-50 text-indigo-700 border-indigo-200 hover:bg-indigo-100',
    keywords: ['steam deck', 'steamdeck']
  }
];

export function PlatformFilter({ onFilterChange, gameCount, allGames = [] }: PlatformFilterProps) {
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [showAll, setShowAll] = useState(true);

  const handlePlatformToggle = (platformId: string) => {
    let newSelected: string[];
    
    if (selectedPlatforms.includes(platformId)) {
      newSelected = selectedPlatforms.filter(id => id !== platformId);
    } else {
      newSelected = [...selectedPlatforms, platformId];
    }
    
    setSelectedPlatforms(newSelected);
    setShowAll(newSelected.length === 0);
    onFilterChange(newSelected);
  };

  const handleShowAll = () => {
    setSelectedPlatforms([]);
    setShowAll(true);
    onFilterChange([]);
  };

  return (
    <div className="w-full bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-10 shadow-sm">
      <div className="max-w-6xl mx-auto px-6 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-bold text-gray-900">Platform Filters</h3>
            <p className="text-sm text-gray-500 mt-1">
              {gameCount} games {selectedPlatforms.length > 0 ? 'match your filters' : 'found'}
            </p>
          </div>

          {selectedPlatforms.length > 0 && (
            <button
              onClick={handleShowAll}
              className="text-sm text-red-600 hover:text-red-700 font-medium flex items-center gap-1 transition-colors"
            >
              <span>✕</span> Clear filters
            </button>
          )}
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap gap-3">
          {/* Show All Button */}
          <button
            onClick={handleShowAll}
            className={`group relative px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
              showAll
                ? 'bg-gray-900 text-white shadow-lg scale-105'
                : 'bg-gray-50 text-gray-700 hover:bg-gray-100 border border-gray-200'
            }`}
          >
            <span className="flex items-center gap-2">
              <span className="text-lg">🌐</span>
              <span>All Platforms</span>
            </span>
            {showAll && (
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/20 to-purple-500/20 -z-10"></div>
            )}
          </button>

          {/* Platform Filter Buttons */}
          {platformOptions.map((platform) => {
            const isSelected = selectedPlatforms.includes(platform.id);
            return (
              <button
                key={platform.id}
                onClick={() => handlePlatformToggle(platform.id)}
                className={`group relative px-6 py-3 rounded-xl font-semibold border-2 transition-all duration-300 ${
                  isSelected
                    ? `${platform.color.replace('50', '100').replace('700', '800')} border-current shadow-lg scale-105`
                    : `${platform.color} border-current hover:shadow-md`
                }`}
              >
                <span className="flex items-center gap-2">
                  <span className="text-lg">{platform.icon}</span>
                  <span>{platform.name}</span>
                </span>
                {isSelected && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                )}
              </button>
            );
          })}
        </div>

        {/* Active Filters Summary */}
        {selectedPlatforms.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 text-sm">
              <span className="font-medium text-blue-800">Active filters:</span>
              <div className="flex flex-wrap gap-1">
                {selectedPlatforms.map((platformId) => {
                  const platform = platformOptions.find(p => p.id === platformId);
                  return platform ? (
                    <span
                      key={platformId}
                      className="inline-flex items-center gap-1 px-2 py-1 bg-white text-blue-700 text-xs font-medium rounded-md border border-blue-200"
                    >
                      <span>{platform.icon}</span>
                      <span>{platform.name}</span>
                    </span>
                  ) : null;
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Helper function to check if a game matches the selected platforms
export function gameMatchesPlatformFilter(gamePlatform: string, selectedPlatforms: string[]): boolean {
  if (selectedPlatforms.length === 0) return true;
  
  const platformLower = gamePlatform?.toLowerCase() || '';
  
  return selectedPlatforms.some(selectedId => {
    const platform = platformOptions.find(p => p.id === selectedId);
    return platform?.keywords.some(keyword => 
      platformLower.includes(keyword.toLowerCase())
    ) || false;
  });
}
