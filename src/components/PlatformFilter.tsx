import { useState } from 'react';
import {
  Gamepad2,
  Monitor,
  Smartphone,
  Laptop,
  Tv,
  Filter,
  RotateCcw
} from 'lucide-react';
import { Game } from '../types';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Card, CardContent } from './ui/card';

interface PlatformFilterProps {
  onFilterChange: (selectedPlatforms: string[]) => void;
  gameCount: number;
  allGames?: Array<{ platform?: string }>;
}

interface PlatformOption {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  keywords: string[];
}

const platformOptions: PlatformOption[] = [
  {
    id: 'playstation',
    name: 'PlayStation',
    icon: Gamepad2,
    color: 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100',
    keywords: ['playstation', 'ps1', 'ps2', 'ps3', 'ps4', 'ps5', 'psp', 'vita']
  },
  {
    id: 'xbox',
    name: 'Xbox',
    icon: Tv,
    color: 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100',
    keywords: ['xbox', 'xbox 360', 'xbox one', 'xbox series']
  },
  {
    id: 'nintendo',
    name: 'Nintendo',
    icon: Gamepad2,
    color: 'bg-red-50 text-red-700 border-red-200 hover:bg-red-100',
    keywords: ['nintendo', 'switch', 'wii', 'gamecube', '3ds', 'ds', 'nes', 'snes']
  },
  {
    id: 'pc',
    name: 'PC',
    icon: Monitor,
    color: 'bg-slate-50 text-slate-700 border-slate-200 hover:bg-slate-100',
    keywords: ['pc', 'windows', 'microsoft windows', 'steam', 'epic']
  },
  {
    id: 'mac',
    name: 'Mac',
    icon: Laptop,
    color: 'bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100',
    keywords: ['mac', 'macos', 'apple']
  },
  {
    id: 'mobile',
    name: 'Mobile',
    icon: Smartphone,
    color: 'bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100',
    keywords: ['ios', 'android', 'iphone', 'ipad', 'mobile']
  },
  {
    id: 'steam',
    name: 'Steam Deck',
    icon: Monitor,
    color: 'bg-indigo-50 text-indigo-700 border-indigo-200 hover:bg-indigo-100',
    keywords: ['steam deck', 'steamdeck']
  }
];

export function PlatformFilter({ onFilterChange, gameCount, allGames = [] }: PlatformFilterProps) {
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [showAll, setShowAll] = useState(true);

  const handlePlatformToggle = (platformId: string) => {
    let newSelected: string[];
    
    if (selectedPlatforms.includes(platformId)) {
      newSelected = selectedPlatforms.filter(id => id !== platformId);
    } else {
      newSelected = [...selectedPlatforms, platformId];
    }
    
    setSelectedPlatforms(newSelected);
    setShowAll(newSelected.length === 0);
    onFilterChange(newSelected);
  };

  const handleShowAll = () => {
    setSelectedPlatforms([]);
    setShowAll(true);
    onFilterChange([]);
  };

  return (
    <div className="w-full bg-background/95 backdrop-blur-md border-b border-border sticky top-0 z-10 shadow-sm">
      <div className="max-w-7xl mx-auto px-6 py-6">
        <Card className="border-primary/20">
          <CardContent className="p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Filter className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-foreground">Platform Filters</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {gameCount} games {selectedPlatforms.length > 0 ? 'match your filters' : 'found'}
                  </p>
                </div>
              </div>

              {selectedPlatforms.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleShowAll}
                  className="text-destructive border-destructive/20 hover:bg-destructive/10"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Clear filters
                </Button>
              )}
            </div>

            {/* Filter Buttons */}
            <div className="flex flex-wrap gap-3">
              {/* Show All Button */}
              <Button
                variant={showAll ? "default" : "outline"}
                onClick={handleShowAll}
                className={`relative px-6 py-3 h-auto font-semibold transition-all duration-300 ${
                  showAll
                    ? 'bg-gradient-to-r from-primary to-orange-600 hover:from-primary/90 hover:to-orange-600/90 shadow-lg scale-105'
                    : 'border-primary/20 hover:bg-primary/5'
                }`}
              >
                <Monitor className="h-4 w-4 mr-2" />
                All Platforms
              </Button>

              {/* Platform Filter Buttons */}
              {platformOptions.map((platform) => {
                const isSelected = selectedPlatforms.includes(platform.id);
                const IconComponent = platform.icon;
                return (
                  <Button
                    key={platform.id}
                    variant={isSelected ? "default" : "outline"}
                    onClick={() => handlePlatformToggle(platform.id)}
                    className={`relative px-6 py-3 h-auto font-semibold border-2 transition-all duration-300 ${
                      isSelected
                        ? `${platform.color.replace('50', '100').replace('700', '800')} border-current shadow-lg scale-105`
                        : `${platform.color} border-current hover:shadow-md`
                    }`}
                  >
                    <IconComponent className="h-4 w-4 mr-2" />
                    {platform.name}
                    {isSelected && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full border-2 border-background"></div>
                    )}
                  </Button>
                );
              })}
            </div>

            {/* Active Filters Summary */}
            {selectedPlatforms.length > 0 && (
              <div className="mt-6 p-4 bg-primary/5 rounded-lg border border-primary/20">
                <div className="flex items-center gap-2 text-sm">
                  <span className="font-medium text-primary">Active filters:</span>
                  <div className="flex flex-wrap gap-2">
                    {selectedPlatforms.map((platformId) => {
                      const platform = platformOptions.find(p => p.id === platformId);
                      const IconComponent = platform?.icon;
                      return platform ? (
                        <Badge
                          key={platformId}
                          variant="secondary"
                          className="bg-primary/10 text-primary border-primary/20"
                        >
                          {IconComponent && <IconComponent className="h-3 w-3 mr-1" />}
                          {platform.name}
                        </Badge>
                      ) : null;
                    })}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Helper function to check if a game matches the selected platforms
export function gameMatchesPlatformFilter(gamePlatform: string, selectedPlatforms: string[]): boolean {
  if (selectedPlatforms.length === 0) return true;
  
  const platformLower = gamePlatform?.toLowerCase() || '';
  
  return selectedPlatforms.some(selectedId => {
    const platform = platformOptions.find(p => p.id === selectedId);
    return platform?.keywords.some(keyword => 
      platformLower.includes(keyword.toLowerCase())
    ) || false;
  });
}
