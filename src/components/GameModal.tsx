import {
  X,
  Gamepad2,
  Monitor,
  Smartphone,
  Laptop,
  Tv,
  Star,
  Calendar,
  User,
  Building,
  Tag
} from 'lucide-react';
import { Game } from '../types';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';

interface GameModalProps {
  game: Game | null;
  isOpen: boolean;
  onClose: () => void;
}

export function GameModal({ game, isOpen, onClose }: GameModalProps) {
  if (!isOpen || !game) return null;

  const getPlatformIcon = (platform: string) => {
    const platformLower = platform?.toLowerCase() || '';
    if (platformLower.includes('playstation') || platformLower.includes('ps')) return Gamepad2;
    if (platformLower.includes('xbox')) return Tv;
    if (platformLower.includes('nintendo') || platformLower.includes('switch')) return Gamepad2;
    if (platformLower.includes('pc') || platformLower.includes('windows')) return Monitor;
    if (platformLower.includes('mac')) return Laptop;
    if (platformLower.includes('ios') || platformLower.includes('iphone')) return Smartphone;
    if (platformLower.includes('android')) return Smartphone;
    if (platformLower.includes('steam')) return Monitor;
    return Gamepad2;
  };

  const PlatformIcon = getPlatformIcon(game.platform || '');

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
      <Card className="max-w-6xl w-full max-h-[90vh] overflow-y-auto shadow-2xl border-primary/20">
        {/* Header */}
        <CardHeader className="sticky top-0 bg-background/95 backdrop-blur-md border-b border-border px-8 py-6 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Gamepad2 className="h-6 w-6 text-primary" />
              </div>
              <div>
                <CardTitle className="text-3xl font-bold bg-gradient-to-r from-primary to-orange-600 bg-clip-text text-transparent">
                  {game.title}
                </CardTitle>
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="secondary" className={
                    game.id.startsWith('igdb_')
                      ? 'bg-blue-100 text-blue-800 border-blue-200'
                      : 'bg-orange-100 text-orange-800 border-orange-200'
                  }>
                    {game.id.startsWith('igdb_') ? 'IGDB' : 'RAWG'}
                  </Badge>
                </div>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-10 w-10 rounded-full hover:bg-muted"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </CardHeader>

        {/* Content */}
        <div className="p-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Cover & Basic Info */}
            <div className="lg:col-span-1">
              {game.cover_image && (
                <div className="aspect-[2/3] bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl overflow-hidden mb-6 shadow-lg">
                  <img
                    src={game.cover_image}
                    alt={game.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}

              {/* Platform */}
              {game.platform && (
                <div className="mb-4">
                  <span className="inline-flex items-center gap-2 px-3 py-2 bg-blue-50 text-blue-700 rounded-lg font-medium border border-blue-200">
                    <span className="text-lg">{getPlatformIcon(game.platform)}</span>
                    {game.platform}
                  </span>
                </div>
              )}

              {/* Rating */}
              {game.metacritic_score && (
                <div className="mb-4">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-700">Metacritic Score:</span>
                    <span className={`px-3 py-1 rounded-lg text-sm font-bold ${
                      game.metacritic_score >= 80 ? 'bg-green-100 text-green-800' :
                      game.metacritic_score >= 60 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {game.metacritic_score}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Details */}
            <div className="lg:col-span-2">
              {/* Genres */}
              {game.genres.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Genres</h3>
                  <div className="flex flex-wrap gap-2">
                    {game.genres.map((genre) => (
                      <span
                        key={genre}
                        className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                      >
                        {genre}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Game Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                {game.developer && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Developer</span>
                    <p className="text-gray-900">{game.developer}</p>
                  </div>
                )}
                
                {game.publisher && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Publisher</span>
                    <p className="text-gray-900">{game.publisher}</p>
                  </div>
                )}
                
                {game.release_date && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Release Date</span>
                    <p className="text-gray-900">{new Date(game.release_date).getFullYear()}</p>
                  </div>
                )}
              </div>

              {/* Description */}
              {game.description && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                  <p className="text-gray-700 leading-relaxed">{game.description}</p>
                </div>
              )}

              {/* Screenshots */}
              {game.screenshots && game.screenshots.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Screenshots</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {game.screenshots.slice(0, 6).map((screenshot, index) => (
                      <div key={index} className="aspect-video bg-gray-200 rounded-lg overflow-hidden">
                        <img
                          src={screenshot}
                          alt={`${game.title} screenshot ${index + 1}`}
                          className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
