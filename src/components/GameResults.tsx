import { useState } from 'react';
import {
  Gamepad2,
  Monitor,
  Smartphone,
  Laptop,
  Tv,
  Star,
  Calendar,
  User,
  Building,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { Game } from '../types';
import { GameModal } from './GameModal';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';

interface GameResultsProps {
  games: Game[];
  loading: boolean;
  error: string | null;
}

export function GameResults({ games, loading, error }: GameResultsProps) {
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleGameClick = (game: Game) => {
    setSelectedGame(game);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedGame(null);
  };
  if (loading) {
    return (
      <div className="w-full max-w-7xl mx-auto p-6">
        <Card className="border-primary/20">
          <CardContent className="p-12">
            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="p-4 bg-primary/10 rounded-2xl">
                  <Loader2 className="h-8 w-8 text-primary animate-spin" />
                </div>
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-2">Searching Games</h3>
              <p className="text-muted-foreground">Discovering games across all platforms...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full max-w-7xl mx-auto p-6">
        <Card className="border-destructive/20">
          <CardContent className="p-12">
            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="p-4 bg-destructive/10 rounded-2xl">
                  <AlertCircle className="h-8 w-8 text-destructive" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">Search Error</h3>
              <p className="text-destructive max-w-md mx-auto leading-relaxed mb-4">
                {error}
              </p>
              <p className="text-muted-foreground max-w-md mx-auto leading-relaxed">
                Please try again with different search terms or check your internet connection.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (games.length === 0 && !loading && !error) {
    return (
      <div className="w-full max-w-7xl mx-auto p-6">
        <Card className="border-muted">
          <CardContent className="p-20">
            <div className="text-center">
              <div className="flex justify-center mb-6">
                <div className="p-6 bg-muted rounded-3xl">
                  <Gamepad2 className="h-12 w-12 text-muted-foreground" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">No games found</h3>
              <p className="text-muted-foreground max-w-md mx-auto leading-relaxed">
                Try adjusting your search terms or platform filters to discover more games in our database.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-primary/10 rounded-2xl">
              <Gamepad2 className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-primary to-orange-600 bg-clip-text text-transparent mb-2">
                Game Library
              </h2>
              <p className="text-muted-foreground">
                {games.length === 0
                  ? "No games match your current filters"
                  : `Discover ${games.length} games across all platforms`
                }
              </p>
            </div>
          </div>

          {games.length > 0 && (
            <div className="text-right">
              <div className="flex gap-4 text-sm mb-2">
                <span className="flex items-center gap-2 px-3 py-1 bg-blue-50 text-blue-700 rounded-full border border-blue-200">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  IGDB: {games.filter(g => g.id.startsWith('igdb_')).length}
                </span>
                <span className="flex items-center gap-2 px-3 py-1 bg-green-50 text-green-700 rounded-full border border-green-200">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  RAWG: {games.filter(g => g.id.startsWith('rawg_')).length}
                </span>
              </div>
              <p className="text-xs text-gray-500">Click any game for details</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-7 2xl:grid-cols-8 gap-6">
        {games.map((game) => (
          <GameCard key={game.id} game={game} onClick={() => handleGameClick(game)} />
        ))}
      </div>

      <GameModal
        game={selectedGame}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}

interface GameCardProps {
  game: Game;
  onClick: () => void;
}

function GameCard({ game, onClick }: GameCardProps) {
  const getPlatformIcon = (platform: string) => {
    const platformLower = platform?.toLowerCase() || '';
    if (platformLower.includes('playstation') || platformLower.includes('ps')) return Gamepad2;
    if (platformLower.includes('xbox')) return Tv;
    if (platformLower.includes('nintendo') || platformLower.includes('switch')) return Gamepad2;
    if (platformLower.includes('pc') || platformLower.includes('windows')) return Monitor;
    if (platformLower.includes('mac')) return Laptop;
    if (platformLower.includes('ios') || platformLower.includes('iphone')) return Smartphone;
    if (platformLower.includes('android')) return Smartphone;
    if (platformLower.includes('steam')) return Monitor;
    return Gamepad2;
  };

  const PlatformIcon = getPlatformIcon(game.platform || '');

  return (
    <Card
      className="group cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-xl border-border/50 hover:border-primary/30 overflow-hidden"
      onClick={onClick}
    >
      {/* Cover Image */}
      <div className="aspect-[2/3] bg-gradient-to-br from-muted to-muted/80 relative overflow-hidden">
        {game.cover_image ? (
          <img
            src={game.cover_image}
            alt={game.title}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            onError={(e) => {
              const target = e.currentTarget;
              target.style.display = 'none';
              const parent = target.parentElement;
              if (parent) {
                parent.innerHTML = `
                  <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-muted to-muted/80">
                    <div class="text-center text-muted-foreground">
                      <div class="mb-2"><svg class="h-12 w-12 mx-auto" fill="currentColor" viewBox="0 0 24 24"><path d="M21 6H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-10 7.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm4-3c-.83 0-1.5-.67-1.5-1.5S14.17 8.5 15 8.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/></svg></div>
                      <div class="text-sm font-medium">No Cover Art</div>
                    </div>
                  </div>
                `;
              }
            }}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-muted to-muted/80">
            <div className="text-center text-muted-foreground">
              <Gamepad2 className="h-12 w-12 mx-auto mb-2" />
              <div className="text-sm font-medium">No Cover Art</div>
            </div>
          </div>
        )}

        {/* Gradient overlay for better badge visibility */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Top badges */}
        <div className="absolute top-3 left-3 right-3 flex justify-between items-start">
          {/* Platform Badge */}
          {game.platform && (
            <Badge variant="secondary" className="bg-background/90 backdrop-blur-sm text-foreground border-border/50 shadow-sm">
              <PlatformIcon className="h-3 w-3 mr-1" />
              {game.platform.length > 10 ? game.platform.substring(0, 10) + '...' : game.platform}
            </Badge>
          )}

          {/* Rating Badge */}
          {game.metacritic_score && (
            <Badge className={`backdrop-blur-sm shadow-sm ${
              game.metacritic_score >= 80
                ? 'bg-green-500 hover:bg-green-600 border-green-400' :
              game.metacritic_score >= 60
                ? 'bg-yellow-500 hover:bg-yellow-600 border-yellow-400' :
                'bg-red-500 hover:bg-red-600 border-red-400'
            }`}>
              <Star className="h-3 w-3 mr-1" />
              {game.metacritic_score}
            </Badge>
          )}
        </div>

        {/* Source Badge */}
        <div className="absolute bottom-3 right-3">
          <Badge variant="secondary" className={`backdrop-blur-sm shadow-sm ${
            game.id.startsWith('igdb_')
              ? 'bg-blue-500/90 text-white border-blue-400/50 hover:bg-blue-600/90'
              : 'bg-orange-500/90 text-white border-orange-400/50 hover:bg-orange-600/90'
          }`}>
            {game.id.startsWith('igdb_') ? 'IGDB' : 'RAWG'}
          </Badge>
        </div>

        {/* Title overlay - appears on hover */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/95 via-black/80 to-transparent text-white p-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
          <h3 className="font-bold text-sm leading-tight line-clamp-2 mb-2">{game.title}</h3>
          <div className="flex items-center justify-between text-xs text-gray-300">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{game.release_date ? new Date(game.release_date).getFullYear() : 'Unknown'}</span>
            </div>
            {game.developer && (
              <div className="flex items-center gap-1 truncate ml-2">
                <User className="h-3 w-3" />
                <span className="truncate">{game.developer}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
}
