import { useState } from 'react';
import { Game } from '../types';
import { GameModal } from './GameModal';

interface GameResultsProps {
  games: Game[];
  loading: boolean;
  error: string | null;
}

export function GameResults({ games, loading, error }: GameResultsProps) {
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleGameClick = (game: Game) => {
    setSelectedGame(game);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedGame(null);
  };
  if (loading) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Searching for games...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Search Error</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (games.length === 0 && !loading && !error) {
    return (
      <div className="w-full max-w-7xl mx-auto p-6">
        <div className="text-center py-20">
          <div className="mb-6">
            <div className="w-24 h-24 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center mb-4">
              <span className="text-4xl">🎮</span>
            </div>
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-3">No games found</h3>
          <p className="text-gray-600 max-w-md mx-auto leading-relaxed">
            Try adjusting your search terms or platform filters to discover more games in our database.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Game Library
            </h2>
            <p className="text-gray-600">
              {games.length === 0
                ? "No games match your current filters"
                : `Discover ${games.length} games across all platforms`
              }
            </p>
          </div>

          {games.length > 0 && (
            <div className="text-right">
              <div className="flex gap-4 text-sm mb-2">
                <span className="flex items-center gap-2 px-3 py-1 bg-blue-50 text-blue-700 rounded-full border border-blue-200">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  IGDB: {games.filter(g => g.id.startsWith('igdb_')).length}
                </span>
                <span className="flex items-center gap-2 px-3 py-1 bg-green-50 text-green-700 rounded-full border border-green-200">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  RAWG: {games.filter(g => g.id.startsWith('rawg_')).length}
                </span>
              </div>
              <p className="text-xs text-gray-500">Click any game for details</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-7 2xl:grid-cols-8 gap-6">
        {games.map((game) => (
          <GameCard key={game.id} game={game} onClick={() => handleGameClick(game)} />
        ))}
      </div>

      <GameModal
        game={selectedGame}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}

interface GameCardProps {
  game: Game;
  onClick: () => void;
}

function GameCard({ game, onClick }: GameCardProps) {
  const getPlatformIcon = (platform: string) => {
    const platformLower = platform?.toLowerCase() || '';
    if (platformLower.includes('playstation') || platformLower.includes('ps')) return '🎮';
    if (platformLower.includes('xbox')) return '🎯';
    if (platformLower.includes('nintendo') || platformLower.includes('switch')) return '🕹️';
    if (platformLower.includes('pc') || platformLower.includes('windows')) return '💻';
    if (platformLower.includes('mac')) return '🖥️';
    if (platformLower.includes('ios') || platformLower.includes('iphone')) return '📱';
    if (platformLower.includes('android')) return '🤖';
    if (platformLower.includes('steam')) return '🚂';
    return '🎮';
  };

  return (
    <div
      className="group cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:z-10"
      onClick={onClick}
    >
      <div className="relative bg-white rounded-lg shadow-lg overflow-hidden border border-gray-100 group-hover:shadow-2xl transition-all duration-300">
        {/* Cover Image */}
        <div className="aspect-[2/3] bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
          {game.cover_image ? (
            <img
              src={game.cover_image}
              alt={game.title}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              onError={(e) => {
                const target = e.currentTarget;
                target.style.display = 'none';
                const parent = target.parentElement;
                if (parent) {
                  parent.innerHTML = `
                    <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-200 to-gray-300">
                      <div class="text-center text-gray-500">
                        <div class="text-4xl mb-2">🎮</div>
                        <div class="text-sm font-medium">No Cover Art</div>
                      </div>
                    </div>
                  `;
                }
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-200 to-gray-300">
              <div className="text-center text-gray-500">
                <div className="text-4xl mb-2">🎮</div>
                <div className="text-sm font-medium">No Cover Art</div>
              </div>
            </div>
          )}

          {/* Subtle overlay for better badge visibility */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

          {/* Top badges */}
          <div className="absolute top-3 left-3 right-3 flex justify-between items-start">
            {/* Platform Badge */}
            {game.platform && (
              <div className="flex items-center gap-1">
                <span className="px-2 py-1 bg-white/90 backdrop-blur-sm text-gray-800 text-xs font-semibold rounded-md shadow-sm border border-white/50">
                  <span className="mr-1">{getPlatformIcon(game.platform)}</span>
                  {game.platform.length > 12 ? game.platform.substring(0, 12) + '...' : game.platform}
                </span>
              </div>
            )}

            {/* Rating Badge */}
            {game.metacritic_score && (
              <span className={`px-2 py-1 text-xs font-bold rounded-md shadow-sm backdrop-blur-sm border ${
                game.metacritic_score >= 80
                  ? 'bg-green-500/90 text-white border-green-400/50' :
                game.metacritic_score >= 60
                  ? 'bg-yellow-500/90 text-white border-yellow-400/50' :
                  'bg-red-500/90 text-white border-red-400/50'
              }`}>
                {game.metacritic_score}
              </span>
            )}
          </div>

          {/* Source Badge */}
          <div className="absolute bottom-3 right-3">
            <span className={`px-2 py-1 text-xs font-medium rounded-md shadow-sm backdrop-blur-sm border ${
              game.id.startsWith('igdb_')
                ? 'bg-blue-500/90 text-white border-blue-400/50'
                : 'bg-green-500/90 text-white border-green-400/50'
            }`}>
              {game.id.startsWith('igdb_') ? 'IGDB' : 'RAWG'}
            </span>
          </div>

          {/* Title overlay - appears on hover */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent text-white p-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
            <h3 className="font-bold text-sm leading-tight line-clamp-2 mb-1">{game.title}</h3>
            <div className="flex items-center justify-between text-xs text-gray-300">
              <span>{game.release_date ? new Date(game.release_date).getFullYear() : 'Unknown'}</span>
              {game.developer && (
                <span className="truncate ml-2">{game.developer}</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
