import { useState, useMemo } from 'react';
import { GameSearch } from './components/GameSearch';
import { GameResults } from './components/GameResults';
import { PlatformFilter, gameMatchesPlatformFilter } from './components/PlatformFilter';
import { Game } from './types';

export default function App() {
  const [allGames, setAllGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);

  // Filter games based on selected platforms
  const filteredGames = useMemo(() => {
    return allGames.filter(game =>
      gameMatchesPlatformFilter(game.platform || '', selectedPlatforms)
    );
  }, [allGames, selectedPlatforms]);

  const handlePlatformFilterChange = (platforms: string[]) => {
    setSelectedPlatforms(platforms);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <GameSearch
        onSearchResults={setAllGames}
        onLoading={setLoading}
        onError={setError}
      />

      {allGames.length > 0 && (
        <PlatformFilter
          onFilterChange={handlePlatformFilterChange}
          gameCount={filteredGames.length}
          allGames={allGames}
        />
      )}

      <GameResults
        games={filteredGames}
        loading={loading}
        error={error}
      />
    </div>
  );
}