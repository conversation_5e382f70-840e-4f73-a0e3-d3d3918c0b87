import { supabase } from './supabase';
interface IGDBGame {
  id: number;
  name: string;
  platforms?: Array<{ name: string }>;
  genres?: Array<{ name: string }>;
  involved_companies?: Array<{
    company: { name: string };
    developer: boolean;
    publisher: boolean;
  }>;
  first_release_date?: number;
  summary?: string;
  cover?: { url: string };
  screenshots?: Array<{ url: string }>;
  aggregated_rating?: number;
  videos?: Array<{ video_id: string; name: string }>;
}

interface RAWGGame {
  id: number;
  name: string;
  platforms?: Array<{ platform: { name: string } }>;
  genres?: Array<{ name: string }>;
  developers?: Array<{ name: string }>;
  publishers?: Array<{ name: string }>;
  released?: string;
  description_raw?: string;
  background_image?: string;
  short_screenshots?: Array<{ image: string }>;
  metacritic?: number;
  rating?: number;
}

interface YouTubeVideo {
  id: { videoId: string };
  snippet: {
    title: string;
    description: string;
    thumbnails: {
      default: { url: string };
      medium: { url: string };
      high: { url: string };
    };
  };
}

interface YouTubeSearchResponse {
  items: YouTubeVideo[];
}

class APIService {
  private igdbAccessToken: string | null = null;
  private igdbTokenExpiry: number = 0;

  constructor() {
    console.log("APIService initialized. Ready to fetch games.");
  }

  private async getIGDBAccessToken(): Promise<string> {
    const clientId = import.meta.env.VITE_IGDB_CLIENT_ID;
    const clientSecret = import.meta.env.VITE_IGDB_CLIENT_SECRET;

    if (!clientId || !clientSecret || clientId === 'your_igdb_client_id' || clientSecret === 'your_igdb_client_secret') {
      throw new Error('IGDB credentials not configured. Please set VITE_IGDB_CLIENT_ID and VITE_IGDB_CLIENT_SECRET in your .env file with valid Twitch API credentials.');
    }

    // Check if we have a valid token
    if (this.igdbAccessToken !== null && Date.now() < this.igdbTokenExpiry) {
      return this.igdbAccessToken;
    }

    try {
      const response = await fetch('https://id.twitch.tv/oauth2/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: clientId,
          client_secret: clientSecret,
          grant_type: 'client_credentials',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get IGDB access token');
      }

      const data = await response.json();
      this.igdbAccessToken = data.access_token;
      this.igdbTokenExpiry = Date.now() + (data.expires_in * 1000) - 60000; // Subtract 1 minute for safety

      return this.igdbAccessToken!;
    } catch (error) {
      console.error('Error getting IGDB access token:', error);
      if (error instanceof Error) {
        throw new Error(`Failed to authenticate with IGDB: ${error.message}`);
      }
      throw new Error('Failed to authenticate with IGDB. Please check your API credentials.');
    }
  }

  private async makeIGDBRequest<T>(endpoint: string, query: string, retryCount: number = 0): Promise<T> {
    const maxRetries = 3;
    const clientId = import.meta.env.VITE_IGDB_CLIENT_ID;

    try {
      const accessToken = await this.getIGDBAccessToken();

      console.log("=== IGDB API Request ===");
      console.log("Endpoint:", endpoint);
      console.log("Client ID:", clientId);
      console.log("Access Token:", accessToken ? "Present" : "Missing");
      console.log("Query:", query);
      console.log("Retry attempt:", retryCount);

      const requestBody = {
        endpoint,
        query,
      };

      console.log("Request body:", JSON.stringify(requestBody, null, 2));

      const { data, error } = await supabase.functions.invoke('igdb-proxy', {
        headers: {
          'X-Client-ID': clientId,
          'X-Authorization': `Bearer ${accessToken}`,
        },
        body: requestBody,
      });

      console.log("=== IGDB API Response ===");
      console.log("Supabase function error:", error);
      console.log("Supabase function data:", data);

      if (error) {
        console.error("Function invocation error:", error);
        
        // Retry on network errors or token issues
        if (retryCount < maxRetries && (
          error.message.includes('network') || 
          error.message.includes('token') ||
          error.message.includes('401')
        )) {
          console.log(`Retrying request (attempt ${retryCount + 1}/${maxRetries})`);
          // Reset token on auth errors
          if (error.message.includes('401') || error.message.includes('token')) {
            this.igdbAccessToken = null;
          }
          await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff
          return this.makeIGDBRequest<T>(endpoint, query, retryCount + 1);
        }
        
        throw new Error(`Function invocation error: ${error.message}`);
      }

      if (data && data.error) {
        console.error("Proxy error from function:", data.error);
        throw new Error(`Proxy error: ${data.error}`);
      }

      if (!data) {
        console.error("No data received from function");
        throw new Error("No data received from IGDB API");
      }

      console.log("Successfully received data from IGDB API");
      return data as T;
    } catch (error) {
      if (retryCount < maxRetries && error instanceof Error && error.message.includes('Failed to authenticate')) {
        console.log(`Retrying authentication (attempt ${retryCount + 1}/${maxRetries})`);
        this.igdbAccessToken = null; // Reset token
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
        return this.makeIGDBRequest<T>(endpoint, query, retryCount + 1);
      }
      throw error;
    }
  }

  async searchRAWGGames(searchTerm: string, limit: number = 10): Promise<RAWGGame[]> {
    const apiKey = import.meta.env.VITE_RAWG_API_KEY || '********************************';

    try {
      // Enhanced RAWG search with better parameters for comprehensive results
      const searchParams = new URLSearchParams({
        search: searchTerm,
        page_size: Math.min(limit, 40).toString(), // RAWG max is 40
        key: apiKey,
        ordering: '-relevance,-rating', // Order by relevance and rating
        search_precise: 'false', // Allow fuzzy matching
      });

      const response = await fetch(
        `https://api.rawg.io/api/games?${searchParams}`
      );

      if (!response.ok) {
        throw new Error(`RAWG API error: ${response.status}`);
      }

      const data = await response.json();
      console.log('RAWG API response:', data);

      return data.results || [];
    } catch (error) {
      console.error('Error searching RAWG games:', error);
      throw error;
    }
  }

  async searchGames(searchTerm: string, limit: number = 10): Promise<IGDBGame[]> {
    try {
      const query = `
        search "${searchTerm}";
        fields name, platforms.name, genres.name, involved_companies.company.name, 
               involved_companies.developer, involved_companies.publisher,
               first_release_date, summary, cover.url, screenshots.url,
               aggregated_rating, videos.video_id, videos.name;
        limit ${limit};
      `;

      console.log('Sending IGDB query:', query);
      const games = await this.makeIGDBRequest<IGDBGame[]>('games', query);
      console.log('Raw IGDB response:', games);
      
      if (!Array.isArray(games)) {
        console.error('IGDB response is not an array:', games);
        return [];
      }
      
      // Map IGDB platform names to our platform names
      const platformMapping: Record<string, string> = {
        'PC (Microsoft Windows)': 'PC',
        'Mac': 'Mac',
        'PlayStation 5': 'PlayStation 5',
        'PlayStation 4': 'PlayStation 4',
        'PlayStation 3': 'PlayStation 3',
        'PlayStation 2': 'PlayStation 2',
        'PlayStation': 'PlayStation',
        'Xbox Series X/S': 'Xbox Series X/S',
        'Xbox One': 'Xbox One',
        'Xbox 360': 'Xbox 360',
        'Xbox': 'Xbox',
        'Nintendo Switch': 'Nintendo Switch',
        'Nintendo 3DS': 'Nintendo 3DS',
        'Nintendo DS': 'Nintendo DS',
        'Wii U': 'Wii U',
        'Wii': 'Wii',
        'Nintendo GameCube': 'GameCube',
        'iOS': 'iOS',
        'Android': 'Android',
        'Steam Deck': 'Steam Deck'
      };

      // Return all games without platform filtering to get comprehensive results
      console.log('IGDB games found:', games.length);

      return games;
    } catch (error) {
      console.error('Error searching games:', error);
      throw error; // Re-throw to let the component handle it
    }
  }

  async searchBothAPIs(searchTerm: string, limit: number = 20): Promise<{ igdb: IGDBGame[], rawg: RAWGGame[] }> {
    try {
      console.log(`Starting comprehensive search for: "${searchTerm}"`);

      // Search both APIs with higher limits to get more comprehensive results
      const [igdbResults, rawgResults] = await Promise.allSettled([
        this.searchGames(searchTerm, limit), // Get full limit from IGDB
        this.searchRAWGGames(searchTerm, limit) // Get full limit from RAWG
      ]);

      const igdbGames = igdbResults.status === 'fulfilled' ? igdbResults.value : [];
      const rawgGames = rawgResults.status === 'fulfilled' ? rawgResults.value : [];

      console.log(`Search results: ${igdbGames.length} from IGDB, ${rawgGames.length} from RAWG`);

      // If we got very few results, try alternative search strategies
      if (igdbGames.length + rawgGames.length < 5) {
        console.log('Few results found, trying alternative search strategies...');

        // Try searching with individual words if the search term has multiple words
        const words = searchTerm.split(' ').filter(word => word.length > 2);
        if (words.length > 1) {
          const alternativeSearches = await Promise.allSettled(
            words.map(word => this.searchRAWGGames(word, 10))
          );

          const additionalResults = alternativeSearches
            .filter(result => result.status === 'fulfilled')
            .flatMap(result => result.value);

          rawgGames.push(...additionalResults);
          console.log(`Added ${additionalResults.length} games from alternative searches`);
        }
      }

      return {
        igdb: igdbGames,
        rawg: rawgGames
      };
    } catch (error) {
      console.error('Error searching both APIs:', error);
      return { igdb: [], rawg: [] };
    }
  }

  // Helper function to normalize game titles for deduplication
  private normalizeTitle(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove special characters
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }

  // Helper function to check if two games are duplicates
  private areGamesDuplicate(game1: any, game2: any): boolean {
    const title1 = this.normalizeTitle(game1.title);
    const title2 = this.normalizeTitle(game2.title);

    // Check if titles are very similar
    if (title1 === title2) return true;

    // Check for common variations
    const variations = [
      [' edition', ''],
      [' remastered', ''],
      [' definitive', ''],
      [' goty', ''],
      [' game of the year', ''],
      [' directors cut', ''],
      [' enhanced', ''],
      [' ultimate', ''],
      [' deluxe', ''],
      [' complete', ''],
      [' hd', ''],
      [' remake', ''],
      [' reboot', '']
    ];

    let cleanTitle1 = title1;
    let cleanTitle2 = title2;

    variations.forEach(([variation, replacement]) => {
      cleanTitle1 = cleanTitle1.replace(variation, replacement);
      cleanTitle2 = cleanTitle2.replace(variation, replacement);
    });

    return cleanTitle1 === cleanTitle2;
  }

  async getGameDetails(gameId: number): Promise<IGDBGame | null> {
    try {
      const query = `
        fields name, platforms.name, genres.name, involved_companies.company.name,
               involved_companies.developer, involved_companies.publisher,
               first_release_date, summary, cover.url, screenshots.url,
               aggregated_rating, videos.video_id, videos.name;
        where id = ${gameId};
      `;

      console.log('Sending IGDB game details query:', query);
      const games = await this.makeIGDBRequest<IGDBGame[]>('games', query);
      console.log('Received game details from IGDB:', games);
      return games[0] || null;
    } catch (error) {
      console.error('Error getting game details:', error);
      return null;
    }
  }

  async searchYouTubeVideos(query: string, maxResults: number = 5): Promise<string[]> {
    const apiKey = import.meta.env.VITE_YOUTUBE_API_KEY;
    
    if (!apiKey) {
      console.warn('YouTube API key not configured');
      return [];
    }

    try {
      const searchQuery = encodeURIComponent(`${query} gameplay trailer review`);
      const response = await fetch(
        `https://www.googleapis.com/youtube/v3/search?part=snippet&type=video&q=${searchQuery}&maxResults=${maxResults}&key=${apiKey}`
      );

      if (!response.ok) {
        throw new Error('YouTube API error');
      }

      const data: YouTubeSearchResponse = await response.json();
      return data.items.map(item => `https://www.youtube.com/watch?v=${item.id.videoId}`);
    } catch (error) {
      console.error('Error searching YouTube videos:', error);
      return [];
    }
  }

  // Helper function to convert RAWG data to our game format
  convertRAWGToGame(rawgGame: RAWGGame): Partial<{
    title: string;
    platform: string;
    genres: string[];
    developer: string;
    publisher: string;
    release_date: string;
    description: string;
    cover_image: string;
    screenshots: string[];
    metacritic_score: number;
    igdb_id: string;
  }> {
    return {
      title: rawgGame.name,
      platform: rawgGame.platforms?.[0]?.platform?.name,
      genres: rawgGame.genres?.map(g => g.name) || [],
      developer: rawgGame.developers?.[0]?.name,
      publisher: rawgGame.publishers?.[0]?.name,
      release_date: rawgGame.released,
      description: rawgGame.description_raw,
      cover_image: rawgGame.background_image,
      screenshots: rawgGame.short_screenshots?.map(s => s.image) || [],
      metacritic_score: rawgGame.metacritic,
      igdb_id: `rawg_${rawgGame.id}`,
    };
  }

  // Helper function to convert IGDB data to our game format
  convertIGDBToGame(igdbGame: IGDBGame): Partial<{
    title: string;
    platform: string;
    genres: string[];
    developer: string;
    publisher: string;
    release_date: string;
    description: string;
    cover_image: string;
    screenshots: string[];
    metacritic_score: number;
    youtube_links: string[];
    igdb_id: string;
  }> {
    const platformMap: Record<string, string> = {
      'PC (Microsoft Windows)': 'PC',
      'Mac': 'Mac',
      'PlayStation 5': 'PlayStation 5',
      'PlayStation 4': 'PlayStation 4',
      'PlayStation 3': 'PlayStation 3',
      'PlayStation 2': 'PlayStation 2',
      'PlayStation': 'PlayStation',
      'Xbox Series X/S': 'Xbox Series X/S',
      'Xbox One': 'Xbox One',
      'Xbox 360': 'Xbox 360',
      'Xbox': 'Xbox',
      'Nintendo Switch': 'Nintendo Switch',
      'Nintendo 3DS': 'Nintendo 3DS',
      'Nintendo DS': 'Nintendo DS',
      'Wii U': 'Wii U',
      'Wii': 'Wii',
      'Nintendo GameCube': 'GameCube',
      'iOS': 'iOS',
      'Android': 'Android',
      'Steam Deck': 'Steam Deck'
    };

    const supportedPlatforms = igdbGame.platforms?.map(p => p.name) || [];
    const matchedPlatform = supportedPlatforms.find(p => platformMap[p]);

    const developers = igdbGame.involved_companies?.filter(ic => ic.developer).map(ic => ic.company.name) || [];
    const publishers = igdbGame.involved_companies?.filter(ic => ic.publisher).map(ic => ic.company.name) || [];

    return {
      title: igdbGame.name,
      platform: matchedPlatform ? platformMap[matchedPlatform] : undefined,
      genres: igdbGame.genres?.map(g => g.name) || [],
      developer: developers[0] || undefined,
      publisher: publishers[0] || undefined,
      release_date: igdbGame.first_release_date 
        ? new Date(igdbGame.first_release_date * 1000).toISOString().split('T')[0]
        : undefined,
      description: igdbGame.summary || undefined,
      cover_image: igdbGame.cover?.url ? `https:${igdbGame.cover.url.replace('t_thumb', 't_cover_big')}` : undefined,
      screenshots: igdbGame.screenshots?.map(s => `https:${s.url.replace('t_thumb', 't_screenshot_med')}`) || [],
      metacritic_score: igdbGame.aggregated_rating ? Math.round(igdbGame.aggregated_rating) : undefined,
      youtube_links: igdbGame.videos?.map(v => `https://www.youtube.com/watch?v=${v.video_id}`) || [],
      igdb_id: igdbGame.id.toString(),
    };
  }
}

export const apiService = new APIService();
export const gameAPI = apiService;